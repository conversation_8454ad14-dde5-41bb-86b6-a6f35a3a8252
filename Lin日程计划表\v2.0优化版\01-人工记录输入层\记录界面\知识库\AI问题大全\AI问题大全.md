
# **臆造问题**
	解释:容易通过"直觉"生成问题,导致不可靠情况
	## 解决方案:
			1.通过网络搜索->综合搜索策略->有效路劲
			2.通过已有解决方案->达成解决问题
			3.类似技术文档->能够真正意义上解决

# **一步到位问题**
	解释:为了直接达成目的 "走捷径" 方式,就是表面看起来是可行的道理.实际上操作就是假的
	原理:我觉得就是一种注意力不够集中的问题,只聚焦目标可以达成的路劲,事实上还有很多需要拆分分解的步骤
	##解决方案:
		1.强制使用任务管理器,拆分任务.可以有效的减缓.
		2.利用"确定性"的架构规矩->来完成拆分
			这里有一个点.就是我的目前使用的八步拆分法
				1.理论概念-方向-拓展关键词
				2.感知概念-感受-讲概念比喻更加容易理解的方式
				3.信息缺口-寻找-缺失原料,更加细节的每一步到达目的
				4.填补缺口-填充-将原料,组装,完成填充
				5.理论路径-尝试-一切理论准备就绪尝试行走
				6.实际操作-验证-实践主义
				7.系统优化-效率-尝试减少不必要步骤
				8.生态闭环-稳定-稳定运作
				这些都是一个流程的步骤.去执行的.

# **记忆容量问题**
	解释:上下文窗口的限制,我觉得也是注意力问题.就是如果文档太多AI就无法正确使用.
	其实核心是能够更加深入了解更多的核心规律.就像人脑一样.真的无法记忆太多东西
	然后通过这些规律,就知道应该怎么做.最后就能过有很好的结果
	##解决方案:
		1.目前应该尝试的就是RAG技术等数据库技术.可能很有效的完成

# 目前最新经验:
	1.使用任务管理器,即便分的再细节,都会因为一次聊天窗口上下文限制导致无法正确运行
		任务管理器只是增加本次对话的质量.
		最有效办法还是一次窗口一次问题
		一个阶段一次新的聊天窗口就是。还是要去搜索。搜索。
	2.一些非常有用的关键词
		2.1:系统性,立体思维,多维系统可视化表达....
		2.2:用心,逐步逐段,模块