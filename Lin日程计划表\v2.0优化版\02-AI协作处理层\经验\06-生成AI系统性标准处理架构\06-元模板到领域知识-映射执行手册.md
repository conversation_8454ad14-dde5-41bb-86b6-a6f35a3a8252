# 元模板→领域模板→领域知识 映射执行手册（v1.0）

> 目的：把“8阶段元认知原模板”与现有的“方向/权威/整合分析/决策”四步体系建立一一映射，形成可操作的执行手册，直接产出标准化的“领域模板”和最终“领域知识”产物。

---

## 1. 概览：两套体系的对齐
- 你的四步体系（日记系统）：
  1) 方向阶段（确定研究方向与问题）
  2) 权威阶段（收集高可信来源）
  3) 整合分析（结构化、比较、得出洞见）
  4) 决策阶段（制定执行路径与依据）
- 8阶段元模板：混沌→定位→关联→叙述→设计→执行→验证→闭环
- 映射关系（核心）：
  - 方向 ≈ 混沌+定位（收集并定位）
  - 权威 ≈ 定位+关联（筛选高证据、建立关键关系）
  - 整合分析 ≈ 关联+叙述（形成结构化叙述与洞见）
  - 决策 ≈ 设计（输出操作蓝图）→ 执行/验证/闭环（落地与沉淀）

---

## 2. 目录与产物规范（建议）
- 产物在“01-人工记录输入层/记录界面/知识库/<主题>/”下生成：
  - 01-<主题>-方向阶段报告.md（对应阶段1-2）
  - 02-<主题>-权威阶段报告.md（对应阶段2-3）
  - 03-<主题>-整合分析报告.md（对应阶段3-4）
  - 04-<主题>-执行路径指南.md（对应阶段5-6）
  - 05-<主题>-AI科学依据指南.md（对应阶段7-8）
- 每个报告包含：目标/输入/产出/质检门槛/证据/下一步触发

---

## 3. 执行清单（按8阶段推进，但可在四步文档中沉淀）

### 阶段1 混沌（方向-素材池）
- 目标：全面收集与主题相关材料（不判断）
- 动作：收集、去重、标注来源时间
- 产出：素材池v0.1 → 汇入“01-方向阶段报告.md”
- 质检：不遗漏、无判断、可追溯

### 阶段2 定位（方向-标签坐标）
- 目标：确定领域/子域/层级/角色/上下文
- 动作：建立标签字典并给素材打标
- 产出：定位清单v0.1 → 汇入“01-方向阶段报告.md”
- 质检：覆盖≥80%，不超过3层

### 阶段3 关联（权威-关系网络）
- 目标：连出关键关系，凸显权威与证据等级
- 动作：构建关系图；标注来源可信度（A/B/C）
- 产出：关系图v0.1 → 汇入“02-权威阶段报告.md”
- 质检：10-20条关键关系，无自相矛盾

### 阶段4 叙述（整合分析-可讲清楚）
- 目标：生成一页纸/三层大纲/FAQ
- 动作：定义术语、梳理逻辑
- 产出：叙述稿v0.1 → 汇入“03-整合分析报告.md”
- 质检：5分钟可复述，概念清晰

### 阶段5 设计（决策-操作蓝图）
- 目标：转为可执行的工单与判据
- 动作：步骤、资源、RACI、风险与应急
- 产出：最小可行工单v0.1 → 汇入“04-执行路径指南.md”
- 质检：可量化、可验收

### 阶段6 执行（决策-实操记录）
- 目标：按工单执行并记录证据
- 动作：过程看板、数据记录、偏差管理
- 产出：执行包v0.1 → 附于“04-执行路径指南.md”
- 质检：遵循度≥阈值；关键字段完整

### 阶段7 验证（科学依据-证据校验）
- 目标：用客观证据评估结果
- 动作：指标/阈值、统计方法、审计轨迹
- 产出：验证报告v0.1 → 汇入“05-AI科学依据指南.md”
- 质检：可重复、可审计

### 阶段8 闭环（科学依据-沉淀）
- 目标：形成可迁移的“领域模板”与最佳实践
- 动作：模板化、复盘、知识库入库
- 产出：领域模板v0.1、最佳实践卡、下次启动卡
- 质检：可迁移、字段完整、示例清晰

---

## 4. 提示词片段（每步最小可用）
- 通用提示头：
  - “你现在作为‘领域模板生成器’，只做两件事：对齐本阶段质检门槛并补全；生成‘下一步最小输入模板’。材料不足则停在当前阶段，输出最小补齐清单。”
- 权威阶段附加：
  - “为每条来源标注证据等级（A系统综述/标准、B高影响期刊、C权威机构/教材、D社区/博客）并说明原因。”
- 验证阶段附加：
  - “明确指标/阈值/统计方法，给出可重复的步骤与数据字段。”

---

## 5. 合并与迭代建议
- 保持04（原模板）不动，用06手册衔接你已有的四步文档与05生成器
- 未来把常用字段沉淀为JSON Schema，便于自动校验与表单化
- 每跑完一个主题，回填到04文档的“原模板建议”中，持续演进

