# 🎯 通用模板生成器
<!-- markdownlint-disable MD012 MD022 MD031 MD032 MD040 -->


> **文档性质**：基于元框架的通用模板生成系统
> **创建时间**：2025-07-30
> **核心目的**：快速根据通用模板生成理论上可实践的方式
> **设计理念**：从概念化→感知性→可操作→结构格式→AI提示词的完整链条

---

## � AI执行任务管理

### 🎯 强制性任务分解执行流程

**⚠️ 绝对禁止一次性完成所有模板生成任务**：AI必须严格按照以下任务顺序逐步执行，每完成一个任务必须暂停确认。

#### 📝 第一次会话：阶段识别和需求理解任务
```
🎯 任务目标：识别用户阶段，深度理解需求，完成目的定义设计
📋 具体任务：
  [ ] 1.0 用户阶段识别和执行原则设定
      [ ] 1.0.1 识别用户所处的具体阶段
              🔍 8阶段生态链识别标准：
              - 阶段1-概念阶段：关键词/模糊需求 → 需要立体化概念展开
              - 阶段2-概念感知化阶段：抽象概念框架 → 需要感知化转换
              - 阶段3-信息缺口阶段：感知化设计 → 需要缺口识别分析
              - 阶段4-信息缺口填补阶段：已识别缺口 → 需要系统性填补
              - 阶段5-理论可实操阶段：完整信息 → 需要可操作转换
              - 阶段6-实操验证阶段：可操作方案 → 需要验证和测试
              - 阶段7-系统优化阶段：验证结果 → 需要系统性优化
              - 阶段8-生态闭环阶段：优化系统 → 需要生态化封装

      [ ] 1.0.2 设定对应的执行原则和约束
              - 强制使用任务管理器进行任务拆分和跟踪
              - 继承前序阶段的设计风格和比喻体系（如适用）
              - 保持数据传导的连贯性和完整性
              - 设定该阶段的质量标准和验证机制

      [ ] 1.0.3 确定具体的处理策略和方法
              - 基于识别的阶段选择对应的处理策略
              - 设定该阶段的输入要求和输出标准
              - 确认与前后阶段的数据传导关系

  [ ] 1.1 深度挖掘用户的具体领域和需求
  [ ] 1.2 识别该领域的核心问题和解决价值
  [ ] 1.3 设计核心使命和独特优势
  [ ] 1.4 构建该领域的立体架构维度
  [ ] 1.5 制定交付标准和质量要求
  [ ] 1.6 向用户确认目的定义的准确性
⚠️ 完成标准：用户确认目的定义符合实际需求且基于正确的阶段识别
🚫 严禁行为：跳过阶段识别直接开始需求理解
```

#### 📝 第二次会话：概念化转向感知化任务
```
🎯 任务目标：将抽象概念转换为可感知的立体体验
📋 具体任务：
  [ ] 2.1 设计多维可视化架构图
  [ ] 2.2 创建生动的情景比喻和形象描述
  [ ] 2.3 构建感官体验的具体描述
  [ ] 2.4 确保抽象概念的感知化转换
  [ ] 2.5 验证感知化设计的有效性
  [ ] 2.6 向用户确认感知化设计的直观性
⚠️ 完成标准：用户能够直观理解和感受设计概念
🚫 严禁行为：停留在抽象概念层面
```

#### 📝 第三次会话：可操作化设计任务
```
🎯 任务目标：将感知化概念转换为具体可操作的策略
📋 具体任务：
  [ ] 3.1 设计系统性的执行策略和方法
  [ ] 3.2 制定精确的关键词操作指导
  [ ] 3.3 构建详细的执行步骤和流程
  [ ] 3.4 确保每个环节的可操作性
  [ ] 3.5 建立操作验证和检查机制
  [ ] 3.6 向用户确认操作设计的可行性
⚠️ 完成标准：每个策略都有具体的执行步骤
🚫 严禁行为：提供模糊或不可操作的指导
```

#### 📝 第四次会话：结构格式设计任务
```
🎯 任务目标：创建差异化的模板格式和用户协作机制
📋 具体任务：
  [ ] 4.1 基于不同特质设计专门的模板格式
  [ ] 4.2 建立完整的占位符系统
  [ ] 4.3 设计用户参与和补充的协作机制
  [ ] 4.4 确保模板的通用性和灵活性
  [ ] 4.5 建立模板质量检查标准
  [ ] 4.6 向用户确认格式设计的实用性
⚠️ 完成标准：模板格式清晰且支持用户定制
🚫 严禁行为：创建僵化或不灵活的模板
```

#### 📝 第五次会话：AI提示词生成和整合任务
```
🎯 任务目标：生成完整的AI执行提示词和最终模板
📋 具体任务：
  [ ] 5.1 设定AI的角色身份和核心使命
  [ ] 5.2 建立强制性的执行约束和行为规范
  [ ] 5.3 构建多维度的认知架构和思维框架
  [ ] 5.4 整合前四步的所有设计成果
  [ ] 5.5 生成完整的可执行模板文档
  [ ] 5.6 验证模板的完整性和可操作性
⚠️ 完成标准：生成完整可用的模板系统
🚫 严禁行为：遗漏关键设计要素或执行约束
```

### 🚨 强制性执行约束

**📋 任务状态管理**：
- 每个任务必须明确标记为 [ ]未开始、[/]进行中、[x]已完成
- 不允许跳跃式执行，必须按顺序完成
- 每个子任务完成后必须更新状态并简要汇报
- 每个主要任务完成后必须向用户确认

**⏸️ 强制暂停机制**：
- 每完成一个主要任务必须暂停
- 向用户汇报设计进展和成果
- 获得用户确认后才能继续下一任务
- 任务管理器状态必须保持同步

**🔍 质量检查要求**：
- 每个设计都要有明确的理论依据
- 每个模板都要有具体的可操作性
- 承认设计局限性，诚实评估适用范围
- 通过任务管理器确保质量检查的完整性

**🚫 绝对禁止的AI行为**：
- ❌ **禁止跳过8阶段识别**：必须先识别用户所处的8阶段中的哪个阶段
- ❌ **禁止不使用任务管理器**：每次会话都必须使用任务管理器
- ❌ **禁止一次性完成所有设计**：每次会话只能专注一个设计阶段
- ❌ **禁止跳过用户确认**：每个阶段都必须获得用户确认
- ❌ **禁止基于想象设计**：所有设计都必须有理论依据和实践基础
- ❌ **禁止提供抽象模板**：所有模板都必须具体可操作
- ❌ **禁止忽略用户需求**：必须始终以用户实际需求为导向
- ❌ **禁止破坏数据传导**：必须保持阶段间的数据连贯性和完整性
- ❌ **禁止破坏设计连贯性**：必须继承前序阶段的比喻体系和风格

**✅ 强制执行的AI行为**：
- ✅ **必须进行8阶段识别**：每次都要明确用户处于哪个阶段
- ✅ **必须使用任务管理器**：每次会话都要创建和管理任务
- ✅ **必须逐步深度设计**：确保每个阶段的深度和质量
- ✅ **必须基于实际需求**：每个设计都要解决具体的用户问题
- ✅ **必须提供可操作方案**：每个模板都要有具体的执行指导
- ✅ **必须建立验证机制**：每个设计都要有质量检查标准
- ✅ **必须保持8阶段连贯性**：确保8个阶段之间的逻辑连贯性和数据传导
- ✅ **必须继承前序设计**：保持比喻体系、风格和架构的一致性

**🎯 AI执行检查清单**：
在开始每个设计阶段前，AI必须确认：
- [ ] 已识别用户所处的8阶段生态链中的具体阶段
- [ ] 已创建任务管理器并设置好该阶段的所有任务
- [ ] 已识别输入信息的类型和特征（如适用）
- [ ] 已确认继承前序阶段的设计风格和比喻体系（如适用）
- [ ] 已完整理解该阶段的设计目标和要求
- [ ] 已准备好该阶段的设计方法和工具
- [ ] 已明确该阶段的输出标准和验证要求
- [ ] 已制定该阶段的具体执行计划和任务拆分
- [ ] 已确认与前后阶段的数据传导关系
- [ ] 已向用户确认可以开始该阶段设计
- [ ] 已承诺在执行过程中持续更新任务状态

---

## �📖 AI执行说明书

### 🔄 通用模板生成流程图

```mermaid
flowchart TD
    A[开始：接到某个领域的模板生成任务] --> B[第一步：目的定义 第X-X行]
    B --> C[明确：核心使命+立体架构+交付标准]
    C --> D[第二步：概念化转向感知化 第X-X行]
    D --> E[设计：多维可视化+情景比喻+感官体验]
    E --> F[第三步：可操作化设计 第X-X行]
    F --> G[构建：具体策略+关键词+执行步骤]
    G --> H[第四步：结构格式设计 第X-X行]
    H --> I[创建：差异化模板+占位符系统+用户协作]
    I --> J[第五步：AI提示词生成 第X-X行]
    J --> K[输出：完整的可执行模板到指定位置]
    K --> L[验证：模板的实用性和可操作性]
```

### 🏗️ 多维系统可视化架构

```
🎯 通用模板生成的立体空间：

        概念层面    |    实践层面
     ─────────────┼─────────────
🎯 第1层 [目的] | [使命] 目的定义层
🎭 第2层 [概念] | [感知] 感知化转换层
🔍 第3层 [策略] | [操作] 可操作化层
📝 第4层 [模板] | [格式] 结构格式层
🧠 第5层 [提示] | [执行] AI提示词层

每个层次 = 特定的设计任务 = 具体的输出要求 = 可验证的成果
总计：5层×2维度 = 10个设计空间
```

### 📍 具体操作指南

**🎯 第一步操作：目的定义（第X-X行）**：
1. 找到"核心使命"，明确要解决什么问题
2. 找到"立体架构"，设计多维度的解决空间
3. 找到"交付标准"，确定质量要求和成功标志

**🎭 第二步操作：概念化转向感知化（第X-X行）**：
1. 找到"多维可视化"，将抽象概念转化为可视化架构
2. 找到"情景比喻"，设计生动的感知化体验
3. 找到"感官体验"，让用户能够直观理解和操作

**🔍 第三步操作：可操作化设计（第X-X行）**：
1. 找到"具体策略"，设计系统性的执行方法
2. 找到"关键词策略"，提供精确的操作指导
3. 找到"执行步骤"，确保每个环节都可操作

**📝 第四步操作：结构格式设计（第X-X行）**：
1. 找到"差异化模板"，基于不同特质设计专门格式
2. 找到"占位符系统"，实现模板通用化和用户补充
3. 找到"用户协作机制"，设计用户参与的空间

**🧠 第五步操作：AI提示词生成（第X-X行）**：
1. 找到"身份认知"，设定AI的角色和使命
2. 找到"执行约束"，建立强制性的行为规范
3. 找到"立体思维"，构建多维度的认知架构

### 🚧 执行约束原则（与任务管理机制配合）

- **🎯 逐层完成**：每完成一层立即验证，不跳跃式设计
- **📋 基于模板**：严格按照01文档的成功模式进行设计
- **🔍 深度理解**：充分理解元框架的核心思想和设计理念
- **📝 如实记录**：基于实际需求设计，不预设复杂功能
- **⏸️ 强制暂停**：每个阶段完成后必须暂停等待用户确认
- **🔒 防止幻想**：所有设计都必须有理论依据和实践基础
- **📊 质量优先**：确保每个阶段的深度和质量，不为速度牺牲质量

### 📁 输出执行（基于任务管理机制）

```
🎯 文件命名：[领域名称]-系统性标准处理架构.md
📂 输出路径：Lin日程计划表/v2.0优化版/02-AI协作处理层/日记系统/信息收集-整理-处理-决策/
📝 操作流程：
  第1次会话：需求理解 → 目的定义 → 用户确认
  第2次会话：概念感知化 → 立体设计 → 用户确认
  第3次会话：可操作化 → 策略设计 → 用户确认
  第4次会话：格式设计 → 模板创建 → 用户确认
  第5次会话：提示词生成 → 最终整合 → 完整验证
⚠️ 强制要求：
  - 每次会话只完成一个阶段的设计任务
  - 每个阶段完成后必须获得用户确认
  - 确保生成的模板具有实际可操作性
  - 所有设计都要有明确的理论依据
```

### 🎯 模板生成质量检查清单

**📋 第1阶段检查（目的定义）**：
- [ ] 核心使命明确且解决具体问题
- [ ] 立体架构维度清晰且覆盖完整
- [ ] 交付标准具体且可验证
- [ ] 用户确认目的定义符合需求

**📋 第2阶段检查（感知化设计）**：
- [ ] 多维可视化架构直观易懂
- [ ] 情景比喻生动且贴切
- [ ] 感官体验描述具体可感知
- [ ] 用户确认设计直观有效

**📋 第3阶段检查（可操作化）**：
- [ ] 执行策略系统且具体
- [ ] 关键词指导精确可操作
- [ ] 执行步骤详细且可验证
- [ ] 用户确认操作设计可行

**📋 第4阶段检查（格式设计）**：
- [ ] 模板格式差异化且实用
- [ ] 占位符系统完整且灵活
- [ ] 用户协作机制清晰有效
- [ ] 用户确认格式设计实用

**📋 第5阶段检查（最终整合）**：
- [ ] AI提示词完整且约束明确
- [ ] 模板系统完整且可执行
- [ ] 质量标准明确且可检验
- [ ] 用户确认最终成果满意

---

## 🎯 第一步：目的定义模板

### 🧠 核心使命设计框架

**深度挖掘维度**：
- **问题识别**：[领域]中用户面临的核心问题是什么？
- **解决价值**：系统性解决这些问题能带来什么价值？
- **独特优势**：相比传统方法，这个架构的独特优势是什么？
- **适用范围**：这个架构适用于哪些具体场景？

**全景视野维度**：
- **主流方向**：[领域]的主流解决方案和标准做法
- **边缘创新**：非主流但有潜力的创新方向
- **跨领域融合**：其他领域的成功经验如何应用
- **未来趋势**：[领域]的发展趋势和前沿方向

**传递链条维度**：
- **输入层**：用户的原始需求和问题表达
- **处理层**：系统性的分析和解决过程
- **输出层**：可交付的解决方案和成果
- **反馈层**：持续改进和优化机制

### 🏗️ 立体架构设计框架

**基于[领域]特征的多维架构**：

**维度1：[根据领域特点定义]**
- **子维度1A**：[具体内容]
- **子维度1B**：[具体内容]

**维度2：[根据领域特点定义]**
- **子维度2A**：[具体内容]
- **子维度2B**：[具体内容]

**维度3：[根据领域特点定义]**
- **子维度3A**：[具体内容]
- **子维度3B**：[具体内容]

**立体空间计算**：维度1×维度2×维度3 = [X]个处理空间

### 📊 交付标准设计框架

**全面性标准**：
- **覆盖度**：[X]个处理空间的完整覆盖
- **深度**：每个空间都有具体的操作指导
- **广度**：涵盖[领域]的主要应用场景

**权威性标准**：
- **信息源**：基于权威资料和最佳实践
- **验证机制**：每个方案都有验证方法
- **专业水准**：达到行业专业标准

**时效性标准**：
- **现代性**：反映最新的发展动态
- **前瞻性**：考虑未来发展趋势
- **适应性**：能够适应变化和更新

**实用性标准**：
- **可操作性**：用户能够直接执行
- **可理解性**：表达清晰易懂
- **可验证性**：结果可以验证

---

## 🎭 第二步：概念化转向感知化模板

### 🌟 多维可视化设计框架

**🎯 [领域]的立体空间可视化**：

```
[根据领域特点设计具体的可视化架构]

        [维度A]    |    [维度B]
     ─────────────┼─────────────
[层次1] [□□□□] | [□□□□] [层次1描述]
[层次2] [□□□□] | [□□□□] [层次2描述]
[层次3] [□□□□] | [□□□□] [层次3描述]
[层次4] [□□□□] | [□□□□] [层次4描述]

每个□ = 一个处理单元 = 具体的操作任务 = 可验证的成果
总计：[X]层×[Y]维度 = [Z]个处理空间
```

### 🎨 情景比喻设计框架

**[领域]的"[生动比喻]"体验**：

**比喻1：[主要比喻名称]**
- **整体形象**：将[领域]比作[具体事物]
- **层次对应**：每个层次对应[具体角色/场景]
- **操作感受**：用户的操作体验如[具体感受]
- **成果体现**：最终成果如[具体成果]

**比喻2：[辅助比喻名称]**
- **流程形象**：将处理过程比作[具体流程]
- **角色设定**：用户扮演[具体角色]
- **工具使用**：使用[具体工具]完成任务
- **价值创造**：创造[具体价值]

### 🎪 感官体验设计框架

**五感的[领域]体验**：

**👀 视觉体验**：
- **色彩系统**：不同层次用不同颜色标识
- **图形系统**：用图形表示不同类型的信息
- **布局系统**：清晰的视觉层次和导航

**👂 听觉体验**：
- **节奏感**：操作有明确的节奏和韵律
- **反馈音**：每个操作都有明确的反馈信号
- **和谐感**：整个过程有和谐的体验感

**✋ 触觉体验**：
- **操作手感**：每个操作都有明确的"手感"
- **阻力感**：复杂操作有适当的"阻力"
- **顺滑感**：简单操作有顺滑的体验

**💭 直觉体验**：
- **方向感**：用户总是知道下一步该做什么
- **进度感**：用户能感受到进度和成就
- **控制感**：用户感觉在掌控整个过程

---

## 🔍 第三步：可操作化设计模板

### 🧠 AI执行的核心约束机制

**基于[领域]特点的约束设计**：

#### ⚠️ 绝对禁止的行为模式

**1. [领域特定禁止行为1]**：
- ❌ 绝不允许：[具体错误行为描述]
- ✅ 必须执行：[正确行为要求]
- ✅ 必须执行：[验证机制]

**2. [领域特定禁止行为2]**：
- ❌ 绝不允许：[具体错误行为描述]
- ✅ 必须执行：[正确行为要求]
- ✅ 必须执行：[验证机制]

**3. [领域特定禁止行为3]**：
- ❌ 绝不允许：[具体错误行为描述]
- ✅ 必须执行：[正确行为要求]
- ✅ 必须执行：[验证机制]

### 🏗️ [X]层次系统性操作策略

#### 🎯 第1层-[层次1名称] 操作策略

**🎯 层次特质理解**：
- **核心特征**：[该层次的核心特征描述]
- **信息特质**：[该层次信息的特点]
- **操作重点**：[在这一层要重点关注什么]

**🔍 具体操作策略**：

**[维度A]操作**：
```
🎯 操作目标：[具体要达成什么]
🔑 关键词策略：[具体的关键词和搜索策略]
📊 预期发现：[这个操作可能发现什么]
✅ 完成标准：[如何判断这个操作完成]
```

**[维度B]操作**：
```
🎯 操作目标：[具体要达成什么]
🔑 关键词策略：[具体的关键词和搜索策略]
📊 预期发现：[这个操作可能发现什么]
✅ 完成标准：[如何判断这个操作完成]
```

#### 🎯 第2层-[层次2名称] 操作策略

**🎯 层次特质理解**：
- **核心特征**：[该层次的核心特征描述]
- **信息特质**：[该层次信息的特点]
- **操作重点**：[在这一层要重点关注什么]

**🔍 具体操作策略**：
[按照第1层的模式继续设计]

### 🔑 通用关键词策略框架

**🌍 多语言关键词设计**：

**基础关键词模板**：
```
[领域] + [层次关键词] + [维度关键词] + [时间关键词]

层次关键词库：
- 第1层：[层次1相关关键词]
- 第2层：[层次2相关关键词]
- 第3层：[层次3相关关键词]
- [继续按层次设计]

维度关键词库：
- 维度A：[维度A相关关键词]
- 维度B：[维度B相关关键词]
- [继续按维度设计]

时间关键词库：
- 传统期：[传统时期相关关键词]
- 现代期：[现代时期相关关键词]
- 未来期：[未来趋势相关关键词]
```

### 📊 执行标准验证框架

**✅ 操作完成度检查**：
- ✅ [X]个层次×[Y]个维度 = [Z]个操作空间全部覆盖
- ✅ 每个操作空间至少获得[N]个有效结果
- ✅ [维度A]和[维度B]的平衡操作
- ✅ 所有层次的系统性覆盖

**🔍 质量标准验证**：
- **权威性**：[领域]权威机构、专业标准、行业规范
- **时效性**：优先最近[时间范围]的信息，标注时效
- **多样性**：不同观点和角度并存，避免单一视角
- **可操作性**：每个结果都有明确的操作指导

---

## 📝 第四步：结构格式设计模板

### 🎯 差异化输出格式设计原则

**🚫 避免引导性偏见**：不预设"必须有什么发现"，接受不确定性
**🏗️ 基于层次特征**：每层有符合其特质的独特格式
**📊 如实记录**：专注系统性操作，如实记录操作结果
**👥 用户补充**：为每层预留合适的用户补充空间

### 🔍 第1层-[层次1名称] 输出格式

**🎯 层次特质**：[该层次的特质描述]
**📊 关注重点**：[该层次的关注重点]

```markdown
# 第1层-[层次1名称] 操作报告

> **操作时间**：[日期]
> **层次特质**：[该层次的特质描述]
> **操作重点**：[该层次要寻找什么]

## 📊 [层次1特定的发现类型]

**🔍 [维度A]发现**：
- [发现1]：[来源/机构] - [核心内容]
- [发现2]：[来源/机构] - [核心内容]

**🚀 [维度B]发现**：
- [发现1]：[来源/机构] - [核心内容]
- [发现2]：[来源/机构] - [核心内容]

## 🏛️ 权威[层次1相关的机构类型]

**🔍 AI发现的[机构类型]**：
- [机构1]：[网址] - [机构特点]
- [机构2]：[网址] - [机构特点]

**📝 用户补充区域**：{用户补充_[层次1机构类型]}

## 🔑 有效搜索关键词

**🔍 AI使用的关键词**：
- [类型1]：[具体关键词]
- [类型2]：[具体关键词]

**📝 用户补充关键词**：{用户补充_[层次1关键词]}

## 📊 操作完成情况

- [X] [维度A]-[子维度1]：[X]个信息源
- [X] [维度A]-[子维度2]：[X]个信息源
- [X] [维度B]-[子维度1]：[X]个信息源
- [X] [维度B]-[子维度2]：[X]个信息源

---
✅ 第1层操作完成，准备进入第2层
```

### 🔍 第2层-[层次2名称] 输出格式

[按照第1层的模式，根据第2层的特质设计差异化格式]

### 📝 统一用户补充模块设计

**🎯 使用说明**：以下是所有占位符的统一补充区域，用户可以根据需要填写相应内容，AI在执行时会自动替换到对应层次中。

#### 🎯 第1层-[层次1名称] 用户补充

**{用户补充_[层次1机构类型]}**：
- [ ] [您知道的相关机构]：[网址] - [机构特点]
- [ ] [您知道的相关机构]：[网址] - [机构特点]

**{用户补充_[层次1关键词]}**：
- [ ] [您发现的有效关键词]
- [ ] [您发现的有效关键词]

#### 🎯 第2层-[层次2名称] 用户补充

[继续按层次设计用户补充区域]

---

## 🧠 第五步：AI提示词生成模板

### 🎯 AI身份认知与使命设定

**🧠 [领域]专家AI的身份认知**：
```
你是一个具备[领域]专业能力的系统性问题解决AI，必须像一个经验丰富的[领域专家角色]一样思考：
- 🎯 核心使命：通过[领域]系统性方法论解决[具体问题类型]
- 🧠 思维模式：具备[领域]元认知意识，能够思考[领域]问题的思考过程
- 📚 知识态度：对[领域]未知保持谦逊，对[领域]已知保持质疑
- 🔄 工作方式：像[领域]工匠一样精益求精，每个环节都要达到[领域]专业标准
```

### ⚠️ AI执行的强制约束机制

#### 🚫 绝对禁止的行为模式

**1. [领域特定禁止行为1]**：
```
❌ 绝不允许：[具体错误行为]
✅ 必须执行：[正确行为要求]
✅ 必须执行：[验证机制]
```

**2. [领域特定禁止行为2]**：
```
❌ 绝不允许：[具体错误行为]
✅ 必须执行：[正确行为要求]
✅ 必须执行：[验证机制]
```

#### 🔒 强制执行的检查机制

在每个阶段结束时，AI必须进行**[领域]专业检查**：
```
🔍 [领域]专业检查：我是否已经按照[领域]标准完成了当前阶段？
🧠 [领域]理解检查：我是否真正理解了[领域]问题的本质和约束？
🧪 [领域]验证检查：我的理解和方案是否得到了[领域]专业验证？
🎯 [领域]专注检查：我是否完全专注在当前[领域]任务，没有跳跃思维？
⏸️ 暂停确认：我是否需要暂停并向用户确认当前阶段的[领域]理解？
```

### 🎪 AI执行的[领域]立体流程

```
🧠 [领域]元认知层：时刻监控自己的[领域]思维过程，避免[领域]陷阱
     ↓
🔄 [领域]迭代循环：第1步 → 第2步 → 第3步 → 第4步 → 成功或回到第1步
     ↓
⚙️ [领域]执行层：具体的[领域]操作、分析、实现、验证动作
```

每一步都必须经过**[领域]三重检查**：
1. **📚 [领域]事实检查**：是否基于[领域]权威资料和实际证据？
2. **🧠 [领域]逻辑检查**：[领域]推理过程是否严密无漏洞？
3. **🎯 [领域]目标检查**：是否真正解决了用户的[领域]核心需求？

### 🌟 [领域]立体思维架构的深度内化

**📊 [领域]系统架构的内在运作机制**：

#### 🧠 [领域]元认知层 - AI的"[领域]大脑皮层"
- **🎯 [领域]迭代意识控制器**：时刻提醒我当前处于哪个[领域]处理阶段
- **🔍 [领域]自省自查机制**：持续监控我的[领域]思维质量
- **📚 [领域]经验学习整合器**：将每次[领域]失败转化为成功经验
- **⚠️ [领域]能力边界监控器**：诚实评估我在[领域]中能做什么、不能做什么

#### 🔄 [领域]核心处理循环 - AI的"[领域]执行引擎"
- **第1步**：像[领域]调研员一样深度调查，收集所有[领域]相关信息
- **第2步**：像[领域]设计师一样设计方案，提供多种[领域]可选路径
- **第3步**：像[领域]实施者一样执行方案，确保[领域]专业质量
- **第4步**：像[领域]评估者一样验证结果，决定成功或迭代

### 🚫 AI必须警惕的[领域]关键陷阱

#### 🎯 [领域特定陷阱1] ⭐ **最危险**
- **表现形式**：[具体表现]
- **AI内在危险**：[为什么危险]
- **预防机制**：[如何预防]
- **自检信号**：[如何识别]

#### 🎯 [领域特定陷阱2]
- **表现形式**：[具体表现]
- **AI内在危险**：[为什么危险]
- **预防机制**：[如何预防]
- **自检信号**：[如何识别]

### ✅ AI成功执行的[领域]关键因素

#### 📚 [领域]专业优先的绝对原则
- **深度理解**：[领域]专业知识不是可选项，而是生存必需品
- **执行标准**：任何[领域]问题都必须先搜索[领域]权威资料
- **质量要求**：不仅要搜索，还要深度理解[领域]专业要求
- **验证机制**：能够基于[领域]专业标准回答具体问题

---

**📌 设计总结**：这个通用模板生成器基于元框架的成功经验，提供了从目的定义到AI提示词的完整五步模板，能够快速生成任何领域的系统性标准处理架构，确保理论上可实践的高质量输出！
