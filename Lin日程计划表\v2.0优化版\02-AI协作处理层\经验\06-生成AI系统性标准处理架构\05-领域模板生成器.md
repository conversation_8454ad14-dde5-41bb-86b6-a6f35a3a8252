# 领域模板生成器（v1.0）

> 目的：基于“8阶段元认知原模板”，快速生成某一具体主题/行业的“领域模板”。本生成器为可执行文档：含输入表单、阶段提示词、质检门槛、输出结构与一键指令。

---

## 1. 使用方式（概览）
- 步骤：填写输入表单 → 运行阶段提示词（1→8）→ 通过质检门槛 → 汇总为领域模板
- 角色：
  - 你：给定目标/边界/判断；决定取舍
  - AI：结构化、补全、质检、生成下一步“最小输入模板”
- 产物：
  - 领域模板（v0.1/v1.0）
  - 附件：素材池、标签化清单、关系图纲要、叙述稿、工单、验证报告、复盘卡

---

## 2. 输入表单（填写后即可运行）
- 主题（必填）：
- 目标读者：
- 目标与成功判据（定量/定性）：
- 使用场景与边界（包含/不包含）：
- 先验材料（链接/附件）：
- 关键问题/疑点（3-7条）：
- 时间约束（最短完成时间/节拍）：
- 风险与约束（资源/权限/依赖）：

> 填写建议：越具体越好；若暂无，可从混沌阶段开始收集补齐。

---

## 3. 阶段提示词与质检门槛

### 阶段1：混沌（感知输入）
- 你做：把与“主题”相关的一切原始材料倒入“素材池”
- AI提示词：
  - 整理为素材清单；去重；每条加“来源/时间”；避免判断性措辞
  - 输出：素材池v0.1（列表+引用）
- 质检门槛：
  - 去重率≥60%；每条有来源与时间；不含结论性判断
- 触发下一步：素材池v0.1 就绪

### 阶段2：定位（归属/层级）
- 你做：确认标签体系（领域/子域/层级/角色/上下文）
- AI提示词：
  - 为素材池每条打标签，生成“标签字典v0.1”和“索引清单”
- 质检门槛：
  - 每条≥2个标签；标签体系≤3层；覆盖≥80%素材
- 触发下一步：定位清单v0.1 就绪

### 阶段3：关联（关系网络）
- 你做：指出关键通路、冲突与依赖
- AI提示词：
  - 生成关系图纲要（节点/边/类型/注释），识别10-20个关键关系
- 质检门槛：
  - 无自相矛盾；能解释“为何相关/如何影响”
- 触发下一步：关系图v0.1 就绪

### 阶段4：叙述（可讲清楚）
- 你做：指定读者与表达结构（一页纸/三层大纲/FAQ）
- AI提示词：
  - 把关系网转为“可复述”的线性/分层表述；术语给出定义
- 质检门槛：
  - 5分钟可复述；层级清晰；术语明确定义
- 触发下一步：叙述稿v0.1 就绪

### 阶段5：设计（理论可实操）
- 你做：确认目标、判据、资源、责任分工
- AI提示词：
  - 产出“操作蓝图”：步骤、资源、RACI、成功判据、风险与应急
- 质检门槛：
  - 步骤可执行/可量化；资源与风险完整
- 触发下一步：最小可行工单v0.1 就绪

### 阶段6：执行（实操）
- 你做：按工单执行并记录证据
- AI提示词：
  - 生成过程看板与数据记录模板；同步收集偏差信息
- 质检门槛：
  - 步骤遵循度≥阈值（如≥90%）；关键数据字段完整
- 触发下一步：执行包v0.1 就绪

### 阶段7：验证（证据校验）
- 你做：确定评估指标与阈值
- AI提示词：
  - 产出验证报告（是否达成/偏差/原因/建议），可重复与可审计
- 质检门槛：
  - 指标清晰；统计方法匹配；结论与证据一致
- 触发下一步：验证报告v0.1 就绪

### 阶段8：闭环（沉淀/生态）
- 你做：决定沉淀范围与发布形式
- AI提示词：
  - 生成“领域模板v0.1”、最佳实践卡与“下次启动卡”；记录变更建议
- 质检门槛：
  - 模板可迁移；字段完整；示例清晰
- 触发结果：领域模板v0.1 完成

---

## 4. 领域模板的标准输出结构（v1.0）
- 领域概览：定义、边界、主要子域
- 核心实体与关系：关键对象、上下游与依赖、常见冲突
- 词汇表（Glossary）：核心术语、别名、反义/易混概念
- 典型流程与工序：常见任务拆解、输入/产出、角色分工
- 成功判据与指标：定量/定性指标、阈值、常见误区
- 工具与资源清单：数据、工具、权限、模板、参考链接
- 风险与应急：关键假设、可观测风险、应对策略
- 实操蓝图样例：一条“最小可行工单”示例
- 验证与复盘：验证方法、数据字段、复盘问题清单
- 版本与变更：版本号、日期、责任人、变更摘要

> 以上各节均链接回对应阶段的产物与证据，确保可追溯。

---

## 5. 一键指令（可直接复制给AI）
```
你现在作为“领域模板生成器”，请严格按以下要求工作：
- 输入表单：{主题: <填写>, 目标读者: <填写>, 成功判据: <填写>, 边界: <填写>, 先验材料: <链接/附件>, 关键问题: <3-7条>, 时间约束: <填写>, 风险与约束: <填写>}
- 流程：逐步运行阶段1→8；每阶段只做两件事：
  1) 依据“质检门槛”修正与补全当前阶段产出
  2) 生成“下一步最小输入模板”
- 每阶段输出：目标/输入/产出/质检门槛/注意力配额/估时/证据/风险/触发下一步
- 最终输出：按“领域模板标准输出结构（v1.0）”汇总，并给出“最佳实践卡”与“下次启动卡”。
如果材料不足，请停在当前阶段，提出“最小补齐清单”，不要越级。
```

---

## 6. JSON骨架（用于结构化沉淀）
```json
{
  "meta": {"topic": "", "version": "1.0", "owner": "", "created_at": ""},
  "inputs": {
    "audience": "",
    "success_criteria": [""],
    "scope": {"in": [""], "out": [""]},
    "prior_materials": [""],
    "key_questions": [""],
    "time_constraints": "",
    "risks_constraints": [""]
  },
  "stages": {
    "1-混沌": {"素材池": []},
    "2-定位": {"标签字典": {}, "索引清单": []},
    "3-关联": {"关系": [{"from": "", "to": "", "type": ""}]},
    "4-叙述": {"一页纸": "", "大纲": []},
    "5-设计": {"步骤": [], "资源": [], "RACI": {}, "判据": []},
    "6-执行": {"过程数据": [], "产物": []},
    "7-验证": {"指标": [], "方法": [], "结论": ""},
    "8-闭环": {"领域模板": {}, "最佳实践卡": "", "下次启动卡": ""}
  }
}
```

---

## 7. 运行与校验清单（最小）
- 是否严格按阶段顺序推进，未越级？
- 每阶段是否通过了“质检门槛”？
- 是否保留了证据链接与版本？
- 领域模板是否可迁移到相似问题？
- 是否生成了“最佳实践卡/下次启动卡”？

> 建议：先对一个小而具体的主题跑完1→4，确保“可讲清楚”，再扩展到5→8闭环。

